<script setup lang="ts">
import Modal from '../atoms/Modal.vue'
import AssignAuditorForm from './AssignAuditorForm.vue'
import type { Institution } from '@/models/types'

const props = defineProps({
  isModalOpen: {
    type: Boolean,
    required: true
  },
  onClose: {
    type: Function,
    required: true
  },
  institution: {
    type: Object as () => Institution | null,
    default: null
  }
})

const handleNext = () => {
  props.onClose()
}
</script>

<template>
  <Modal :isOpen="isModalOpen" :onClose="onClose">
    <div class="flex flex-col gap-6 max-w-4xl w-full">
      <div class="flex flex-col gap-2">
        <h2 class="text-xl font-bold">Editar Configuración del Proceso</h2>
        <p class="text-sm text-gray-600">
          Modifica la configuración del proceso para la institución 
          <span class="font-semibold">{{ institution?.name }}</span>
        </p>
      </div>

      <div class="border-t border-gray-200"></div>

      <div v-if="institution" class="flex-1">
        <AssignAuditorForm
          :institution="institution"
          :isProcessModification="true"
          :onNext="handleNext"
        />
      </div>
    </div>
  </Modal>
</template>

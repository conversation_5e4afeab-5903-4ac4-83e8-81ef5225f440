<script setup lang="ts">
import { useAuditors } from '@/hooks/useAuditors'
import Button from '../atoms/Button.vue'
import { useInstitutions } from '@/hooks/useInstitutions'
import { useProcesses } from '@/hooks/process/useProcesses'
import type { Institution, Process } from '@/models/types'
import { ref, onMounted, computed, watch } from 'vue'
import { useToast } from 'vue-toast-notification'
import { convertUTCToLocalNaiveDate, formatDateToYYYYMMDD } from '@/helpers/formatters/dateFormatters'

const toast = useToast()

const props = defineProps({
  institution: {
    type: Object as () => Institution,
    required: false
  },
  onNext: {
    type: Function,
    required: true
  },
  isProcessModification: {
    type: Boolean,
    default: false
  }
})

const { handleAssignAuditorToInstitution } = useInstitutions()
const { handleGetActualProcessByInstitutionID, handleUpdateProcessQuorum } = useProcesses()
const selectedAuditorID = ref<number>(props.institution?.auditor?.id ?? -1)

const { auditors, isLoading, handleSearchAuditors, handleGetAuditorByID } = useAuditors()

const currentAuditorInList = computed(() => {
  if (selectedAuditorID.value === -1) return false
  return auditors.value.some((auditor) => auditor.id === selectedAuditorID.value)
})

// Process-related refs
const actualProcess = ref<Process | null>(null)
const startDate = ref<string>('')
const endDate = ref<string>('')
const quorumPercentage = ref<number>(50)

const handleSubmit = async () => {
  if (props.institution) {
    try {
      // Always assign auditor
      await handleAssignAuditorToInstitution(props.institution.id, selectedAuditorID.value)

      // If it's process modification and we have a process, update quorum
      if (props.isProcessModification && actualProcess.value) {
        const quorumDecimal = quorumPercentage.value / 100
        await handleUpdateProcessQuorum(actualProcess.value.id, quorumDecimal)
        toast.success('Proceso actualizado correctamente')
      }

      props.onNext()
    } catch (error) {
      console.error('Error updating process:', error)
      toast.error('Error al actualizar el proceso')
    }
  }
}

// Load actual process when in modification mode
const loadActualProcess = async () => {
  if (props.isProcessModification && props.institution) {
    try {
      const process = await handleGetActualProcessByInstitutionID(props.institution.id)
      if (process) {
        actualProcess.value = process

        // Set form values from process
        if (process.startDate) {
          startDate.value = formatDateToYYYYMMDD(convertUTCToLocalNaiveDate(process.startDate))
        }
        if (process.endDate) {
          endDate.value = formatDateToYYYYMMDD(convertUTCToLocalNaiveDate(process.endDate))
        }
        if (process.approvalThreshold !== undefined) {
          quorumPercentage.value = Math.round(process.approvalThreshold * 100)
        }
        if (process.status) {
          selectedProcessStatus.value = process.status
        }
      }
    } catch (error) {
      console.error('Error loading actual process:', error)
      toast.error('Error al cargar el proceso actual')
    }
  }
}

onMounted(async () => {
  await handleSearchAuditors('')

  if (props.institution?.auditor?.id && !currentAuditorInList.value) {
    await handleGetAuditorByID(props.institution.auditor.id)
  }

  // Load process data if in modification mode
  await loadActualProcess()
})

// Watch for changes in institution or modification mode
watch([() => props.institution, () => props.isProcessModification], async () => {
  await loadActualProcess()
})

const PROCESS_STATUS = {
  UNINITIATED: 'No iniciado',
  IN_PROGRESS: 'En progreso',
  SURVEY_FINISHED: 'Encuestas finalizadas',
  REJECTED: 'Rechazado',
  AUDIT: 'Auditoría',
  APPEALABLE: 'Apelable',
  APPEAL: 'Apelación',
  FINISHED: 'Finalizado'
}

const selectedProcessStatus = ref<string>(PROCESS_STATUS.UNINITIATED)
</script>

<template>
  <form
    class="flex flex-col h-full w-full items-start justify-between box-border gap-5 pl-3"
    @submit.prevent="handleSubmit"
    aria-labelledby="form-title"
    novalidate
  >
    <fieldset class="flex flex-col gap-3 w-full">
      <legend>
        <h2 class="text-body3 font-medium text-neutral-950">Configuración de auditor</h2>
      </legend>

      <div
        class="flex flex-col gap-1.5 text-body4 pl-3 pt-1.5 w-full h-fit self-start text-sm font-medium text-neutral-1000"
        style="width: 100%"
      >
        <label for="auditor-select">Auditor seleccionado</label>
        <select
          id="auditor-select"
          name="auditor"
          class="flex flex-col w-full border px-3 py-2 rounded border-neutral-400 bg-white"
          v-model="selectedAuditorID"
          :disabled="isLoading"
          :aria-describedby="isLoading ? 'auditor-loading' : undefined"
          required
          aria-required="true"
        >
          <option v-if="selectedAuditorID === -1" disabled value="-1">
            {{ isLoading ? 'Cargando auditores...' : 'Selecciona un auditor' }}
          </option>

          <option
            v-if="props.institution?.auditor && !currentAuditorInList && selectedAuditorID !== -1"
            :value="props.institution.auditor.id"
          >
            {{ props.institution.auditor.username }} (Actual)
          </option>

          <option v-for="auditor in auditors" :key="auditor.id" :value="auditor.id">
            {{ auditor.username }}
            {{ auditor.id === props.institution?.auditor?.id ? ' (Actual)' : '' }}
          </option>
        </select>
        <div v-if="isLoading" id="auditor-loading" class="sr-only" aria-live="polite">
          Cargando lista de auditores
        </div>
      </div>
    </fieldset>

    <fieldset class="flex flex-col h-fit w-full">
      <legend>
        <h2 class="text-body3 text-neutral-950">Configuración de proceso</h2>
      </legend>

      <div class="flex flex-col gap-1.5 pt-1.5">
        <fieldset class="flex flex-row gap-4">
          <label
            for="start-date"
            class="flex flex-col gap-1.5 text-body4 pl-3 w-full h-fit self-start text-sm font-medium text-neutral-1000"
          >
            Fecha de inicio
            <input
              id="start-date"
              name="startDate"
              type="date"
              v-model="startDate"
              class="flex flex-col w-full border px-3 py-2 rounded border-neutral-400 bg-white"
              :readonly="true"
              :required="false"
              :aria-required="false"
            />
          </label>

          <label
            for="end-date"
            class="flex flex-col gap-1.5 text-body4 pl-4 w-full h-fit self-start text-sm font-medium text-neutral-1000"
          >
            Fecha de término
            <input
              id="end-date"
              name="endDate"
              type="date"
              v-model="endDate"
              class="flex flex-col w-full border px-3 py-2 rounded border-neutral-400 bg-white"
              :readonly="true"
              :required="false"
              :aria-required="false"
            />
          </label>

          <label
            for="process-status"
            class="flex flex-col gap-1.5 text-body4 pl-4 w-full h-fit self-start text-sm font-medium text-neutral-1000"
          >
            Estado del proceso
            <select
              id="process-status"
              name="processStatus"
              class="flex flex-col w-full border px-3 py-2 rounded border-neutral-400 bg-white"
              v-model="selectedProcessStatus"
              :disabled="true"
              :required="false"
              :aria-required="false"
            >
              <option v-for="(label, key) in PROCESS_STATUS" :key="key" :value="key">
                {{ label }}
              </option>
            </select>
          </label>
        </fieldset>

        <fieldset
          class="flex flex-col gap-1.5 text-body4 pl-3 w-full h-fit self-start text-sm font-medium text-neutral-1000"
          style="width: 100%"
        >
          <label for="quorum-percentage">
            <legend>Quórum de aprobación</legend>
            <input
              id="quorum-percentage"
              name="quorumPercentage"
              type="number"
              min="0"
              max="100"
              step="1"
              v-model="quorumPercentage"
              placeholder="Ejemplo: 50"
              class="flex flex-col w-full border px-3 py-2 rounded border-neutral-400 bg-white"
              aria-describedby="quorum-help quorum-info"
              :readonly="!isProcessModification"
              :required="!isProcessModification"
              :aria-required="!isProcessModification"
            />
          </label>

          <p id="quorum-help" class="text-body5 italic text-neutral-800">
            El valor debe ser entre 0% y 100%. El valor por defecto es 50%
          </p>

          <aside
            id="quorum-info"
            class="2xl:text-sm text-xs text-neutral-800 bg-blue-400 border border-blue-400 bg-opacity-10 p-2 rounded-md text-start mt-1 w-full"
            role="note"
            aria-labelledby="info-title"
          >
            <div class="flex justify-start items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-5 h-5 text-blue-500"
                fill="#dbeafe"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
                role="img"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4m0 4h.01M21 12c0-5.523-4.478-10-10-10S1 6.477 1 12s4.478 10 10 10 10-4.477 10-10z"
                />
              </svg>
              <p class="break-words whitespace-normal text-blue-500">
                <strong id="info-title" class="text-blue-500">Información: </strong>
                El quórum de aprobación determina el porcentaje mínimo de respuestas positivas
                requerido para aprobar cada dimensión del proceso.
              </p>
            </div>
          </aside>
        </fieldset>
      </div>
    </fieldset>

    <footer class="flex w-full justify-end items-center box-border pt-6">
      <Button
        :disabled="selectedAuditorID === -1"
        type="submit"
        variant="primary"
        :aria-label="
          selectedAuditorID === -1
            ? 'Selecciona un auditor para continuar'
            : isProcessModification
              ? 'Actualizar configuración de proceso y auditoría'
              : 'Actualizar configuración de auditoría'
        "
      >
        {{ isProcessModification ? 'Actualizar Proceso' : 'Actualizar' }}
      </Button>
    </footer>
  </form>
</template>

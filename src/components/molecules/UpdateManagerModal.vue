<script setup lang="ts">
import Modal from '../atoms/Modal.vue';
import Input from '../atoms/Input.vue';
import Button from '../atoms/Button.vue';
import type { Gestor } from '@/models/types';
import { ref, toRef, toRefs } from 'vue';
import useVuelidate from '@vuelidate/core';
import { COMMON_RULES } from '@/models/validation/formRules';
import type { GestorFormSchema } from '@/models/validation/formSchemas';
import { useManagers } from '@/hooks/useManagers';
import { useToast } from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';
import { useForm } from '@/hooks/useForm';

const $toast = useToast();
const props = defineProps({
    manager: {
        type: Object as () => Gestor,
        required: true
    },
    isModalOpen: {
        type: Boolean,
        required: true
    },
    onClose: {
        type: Function,
        required: true
    }
})

const { manager } = toRefs(props);

const managerDataRef = ref({
    email: props.manager.email
})

const $vUpdateManager = useVuelidate({ email: COMMON_RULES.email }, managerDataRef.value);
const { handleUpdateManager } = useManagers();

const { formData: managerData, isLoading, handleSubmit, hasChanges } = useForm({
    initialDataRef: toRef(managerDataRef),
    validationRules: { email: COMMON_RULES.email },
    updateOnlyModified: true,
    updateCondition: toRef(true),
    onSubmit: async (data) => {
        await submitHandler(data);
        props.onClose();
    }
});

const submitHandler = async (managerData: { email: string }) => {
    const result = await $vUpdateManager.value.$validate();
    if (result) {
        try {
            await handleUpdateManager(props.manager.id, managerData as GestorFormSchema);
            $toast.success('Gestor actualizado correctamente');
            props.onClose();
        } catch (error) {
            $toast.error('Error al actualizar el gestor');
        }
    }
}


</script>

<template>
    <Modal :isOpen="isModalOpen" :onClose="onClose">
        <div class="flex w-full max-w-[95vw] sm:max-w-[80vw] md:max-w-[60vw] lg:max-w-[50vw] xl:max-w-[40vw] flex-col gap-6 sm:gap-8 text-base">
            <h3 class="flex flex-col gap-2 text-lg sm:text-xl font-bold">Editar información de Gestor
                <p class="text-sm font-medium">
                    Actualice los datos del gestor en caso de ser necesario.
                </p>
            </h3>
            <div class="w-full border-b border-black/10"></div>
            <form @submit.prevent="handleSubmit" class="flex flex-col gap-8">
                <div class="flex flex-col gap-6">
                    <div class="flex flex-col sm:flex-row sm:items-center justify-start gap-4 sm:gap-8 w-full font-normal">
                        <span class="font-semibold text-black/50 w-full sm:w-20">Nombre</span>
                        <Input v-model="manager.username" :disabled="true" class="w-full sm:w-80" />
                    </div>
                    <div class="flex flex-col sm:flex-row sm:items-center justify-start gap-4 sm:gap-8 w-full font-normal">
                        <span class="font-semibold text-black/50 w-full sm:w-20">Email</span>
                        <Input :errorMessage="$vUpdateManager.email.$errors[0]?.$message.toString()"
                            v-model="managerData.email" class="w-full sm:w-80" />
                    </div>
                </div>
                <div class="flex w-full items-center justify-center pt-4">
                    <Button :loading="isLoading" :disabled="$vUpdateManager.$errors.length > 0 || !hasChanges"
                        type="submit" class="w-48">
                        Actualizar
                    </Button>
                </div>
            </form>
        </div>
    </Modal>
</template>